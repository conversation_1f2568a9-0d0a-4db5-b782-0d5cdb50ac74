# Docker构建优化指南

## 问题分析

原始Dockerfile存在以下性能问题：
1. **重复的yarn install**：在deps和builder阶段都执行了完整的依赖安装
2. **缓存利用不足**：没有使用Docker BuildKit的缓存挂载功能
3. **网络超时设置不当**：默认的网络超时可能导致大包下载失败
4. **构建上下文过大**：虽然有.dockerignore，但仍可优化

## 优化方案

### 1. 使用优化后的Dockerfile

我已经创建了两个优化版本：

#### `Dockerfile`（已优化）
- 使用缓存挂载加速yarn install
- 分离生产依赖和开发依赖的安装
- 优化yarn配置

#### `Dockerfile.optimized`（超级优化版）
- 更细粒度的多阶段构建
- 并行化依赖安装
- 最大化缓存利用率

### 2. 使用BuildKit构建

确保启用Docker BuildKit：

```bash
# 方法1：环境变量
export DOCKER_BUILDKIT=1
export COMPOSE_DOCKER_CLI_BUILD=1

# 方法2：使用优化构建脚本
./build-optimized.sh
```

### 3. 构建命令对比

#### 原始构建（慢）
```bash
docker-compose -f docker-compose.simple.yml build --no-cache
```

#### 优化构建（快）
```bash
# 使用默认优化Dockerfile
DOCKER_BUILDKIT=1 docker-compose -f docker-compose.simple.yml build

# 使用超级优化版本
DOCKER_BUILDKIT=1 docker-compose -f docker-compose.simple.yml build --file Dockerfile.optimized
```

### 4. 性能提升预期

根据优化措施，预期性能提升：

- **首次构建**：30-50%的时间减少
- **增量构建**：70-90%的时间减少（当只有代码变更时）
- **网络问题环境**：显著提升稳定性

## 使用建议

### 开发环境
```bash
# 使用优化构建脚本
./build-optimized.sh
```

### 生产环境
```bash
# 使用超级优化版本
export DOCKER_BUILDKIT=1
docker build -f Dockerfile.optimized -t lung-function-admin:latest .
```

### CI/CD环境
```bash
# 在CI/CD中启用BuildKit和缓存
export DOCKER_BUILDKIT=1
docker build \
  --cache-from lung-function-admin:latest \
  --build-arg BUILDKIT_INLINE_CACHE=1 \
  -t lung-function-admin:latest .
```

## 进一步优化建议

### 1. 使用yarn的离线模式
如果网络是主要瓶颈，可以考虑：
```bash
# 在本地生成离线缓存
yarn install --prefer-offline
```

### 2. 使用多阶段构建缓存
```bash
# 构建时指定缓存来源
docker build --cache-from lung-function-admin:deps --target deps .
```

### 3. 使用Docker Registry缓存
```bash
# 推送中间层到registry
docker build --cache-from registry.example.com/app:cache .
```

## 故障排除

### 如果构建仍然很慢
1. 检查网络连接到腾讯云镜像
2. 确认Docker BuildKit已启用
3. 清理Docker缓存：`docker system prune -a`
4. 检查磁盘空间是否充足

### 如果出现缓存问题
```bash
# 清理yarn缓存
docker run --rm -v $(pwd):/app -w /app node:18-alpine yarn cache clean

# 强制重新构建
docker-compose -f docker-compose.simple.yml build --no-cache
```

## 监控构建性能

使用以下命令监控构建时间：
```bash
time docker-compose -f docker-compose.simple.yml build
```

记录优化前后的构建时间，以量化改进效果。
